{"name": "occorocks", "version": "0.3.1", "description": "A monorepo template built with Bun, Hono, Vite, and React", "author": "<PERSON>", "license": "MIT", "homepage": "https://github.com/steved<PERSON>ev/bhvr", "workspaces": ["./server", "./client", "./shared"], "scripts": {"dev:client": "cd client && bun run dev", "dev:server": "cd server && bun run dev", "dev:shared": "cd shared && bun run dev", "dev": "concurrently \"bun run dev:shared\" \"bun run dev:server\" \"bun run dev:client\"", "build:client": "cd client && bun run build", "build:shared": "cd shared && bun run build", "build:server": "cd server && bun run build", "build": "bun run build:shared && bun run build:server && bun run build:client", "build:single": "bun run build && bun run copy:static && bun run build:server", "copy:static": "rm -rf server/static && cp -r client/dist server/static", "start:single": "cd server && bun run dist/index.js", "changetopostinstall": "bun run build:shared && bun run build:server"}, "keywords": ["bun", "hono", "react", "vite", "monorepo"], "devDependencies": {"@biomejs/biome": "2.1.2", "bun-types": "latest", "concurrently": "^9.1.2"}, "peerDependencies": {"typescript": "^5.7.3"}, "dependencies": {"arktype": "^2.1.20", "hono": "^4.8.5", "ogl": "^1.0.11", "postgres": "^3.4.7"}, "trustedDependencies": ["@tailwindcss/oxide"]}