import { SiDiscord } from "@icons-pack/react-simple-icons";
import type { DiscordApiResponse } from "shared/dist";

interface DiscordStatusProps {
  user: DiscordApiResponse["data"];
  error?: any;
  className?: string;
}

export function DiscordStatus({ user, error, className = "" }: DiscordStatusProps) {
  if (!error && !user) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <SiDiscord className="w-4 h-4 text-[#5865F2]" />
        <span className="text-sm text-white/75">Loading Discord status...</span>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <SiDiscord className="w-4 h-4 text-white/50" />
        <span className="text-sm text-white/50">Discord status unavailable</span>
      </div>
    );
  }

  const { discord_user, discord_status, activities } = user;

  // Get status color based on Discord status
  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-green-500";
      case "idle":
        return "bg-yellow-500";
      case "dnd":
        return "bg-red-500";
      case "offline":
      default:
        return "bg-gray-500";
    }
  };

  // Get the primary activity (usually the first non-custom status)
  const primaryActivity = activities.find(
    (activity) => activity.type !== 4 // Type 4 is custom status
  );

  const customStatus = activities.find((activity) => activity.type === 4);

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <div className="relative">
        <img
          src={`https://cdn.discordapp.com/avatars/${discord_user.id}/${discord_user.avatar}.png?size=32`}
          alt={`${discord_user.display_name}'s avatar`}
          className="w-8 h-8 rounded-full"
        />
        <div
          className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background ${getStatusColor(
            discord_status
          )}`}
        />
      </div>

      <div className="flex flex-col">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-white">
            {discord_user.display_name}
          </span>
          {discord_user.primary_guild && (
            <span className="text-xs text-white/60 bg-white/10 px-1.5 py-0.5 rounded">
              {discord_user.primary_guild.tag}
            </span>
          )}
        </div>

        {customStatus?.state && (
          <span className="text-xs text-white/75">{customStatus.state}</span>
        )}

        {primaryActivity && (
          <div className="text-xs text-white/60">
            {primaryActivity.details && (
              <span>{primaryActivity.details}</span>
            )}
            {primaryActivity.state && primaryActivity.details && (
              <span> - {primaryActivity.state}</span>
            )}
            {primaryActivity.state && !primaryActivity.details && (
              <span>{primaryActivity.state}</span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
