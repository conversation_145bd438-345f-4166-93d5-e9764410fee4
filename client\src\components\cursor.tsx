import {
	AnimatePresence,
	motion,
	useMotionValue,
	useSpring,
} from "framer-motion";
import { useCallback, useEffect, useRef, useState } from "react";

export interface CursorProps {
	targetSelector?: string;
	spinDuration?: number;
	hideDefaultCursor?: boolean;
}

interface HoveredTarget {
	element: Element;
	rect: DOMRect;
}

export default function Cursor({
	targetSelector = ".cursor-target",
	spinDuration = 2,
	hideDefaultCursor = true,
}: CursorProps) {
	const [hoveredTarget, setHoveredTarget] = useState<HoveredTarget | null>(null);
	const shakeTimeoutRef = useRef<NodeJS.Timeout | undefined>(null);

	const cursorX = useMotionValue(0);
	const cursorY = useMotionValue(0);

	const springConfig = { damping: 25, stiffness: 700, mass: 0.5 };
	const x = useSpring(cursorX, springConfig);
	const y = useSpring(cursorY, springConfig);

	const moveCursor = useCallback(
		(clientX: number, clientY: number) => {
			cursorX.set(clientX);
			cursorY.set(clientY);
		},
		[cursorX, cursorY],
	);

	useEffect(() => {
		const originalCursor = document.body.style.cursor;
		if (hideDefaultCursor) {
			document.body.style.cursor = "none";
		}

		cursorX.set(window.innerWidth / 2);
		cursorY.set(window.innerHeight / 2);

		const moveHandler = (e: MouseEvent) => moveCursor(e.clientX, e.clientY);

		const overHandler = (e: MouseEvent) => {
			const directTarget = e.target as Element;
			const target = directTarget.closest(targetSelector);

			if (!target) return;

			if (shakeTimeoutRef.current) {
				clearTimeout(shakeTimeoutRef.current);
				shakeTimeoutRef.current = undefined;
			}

			const rect = target.getBoundingClientRect();
			setHoveredTarget({ element: target, rect });
		};

		const outHandler = (e: MouseEvent) => {
			const directTarget = e.target as Element;
			const relatedTarget = e.relatedTarget as Element;

			// stupid fucking button fix
			const currentTarget = directTarget.closest(targetSelector);
			const nextTarget = relatedTarget?.closest(targetSelector);

			if (!currentTarget) return;
			if (nextTarget && nextTarget !== currentTarget) return;
			if (nextTarget === currentTarget) return;

			if (shakeTimeoutRef.current) {
				clearTimeout(shakeTimeoutRef.current);
			}

			shakeTimeoutRef.current = setTimeout(() => {
				setHoveredTarget(null);
			}, 50);
		};

		window.addEventListener("mousemove", moveHandler);
		document.addEventListener("mouseover", overHandler, true);
		document.addEventListener("mouseout", outHandler, true);

		return () => {
			window.removeEventListener("mousemove", moveHandler);
			document.removeEventListener("mouseover", overHandler, true);
			document.removeEventListener("mouseout", outHandler, true);
			document.body.style.cursor = originalCursor;
			if (shakeTimeoutRef.current) {
				clearTimeout(shakeTimeoutRef.current);
			}
		};
	}, [targetSelector, moveCursor, cursorX, cursorY, hideDefaultCursor]);

	return (
		<>
			{/* cross */}
			<motion.div
				className="fixed top-0 left-0 w-0 h-0 pointer-events-none z-[9999] mix-blend-difference"
				style={{ x, y }}
			>
				<motion.div
					className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"
					animate={hoveredTarget ? { rotate: 0 } : { rotate: 360 }}
					transition={
						hoveredTarget
							? { duration: 0.3 }
							: { duration: spinDuration, repeat: Infinity, ease: "linear" }
					}
				>
					<div
						className="absolute w-px h-2 bg-white -translate-x-1/2"
						style={{ top: "-8px" }}
					/>
					<div className="absolute w-4 h-px bg-white -translate-x-1/2 -translate-y-1/2" />
					<div
						className="absolute w-px h-2 bg-white -translate-x-1/2"
						style={{ top: "4px" }}
					/>
				</motion.div>
			</motion.div>

			{/* hovered border */}
			<AnimatePresence>
				{hoveredTarget && (
					<motion.div
						className="fixed pointer-events-none z-[9998] border-2 border-dashed border-white/30"
						initial={{ opacity: 0 }}
						animate={{
							opacity: 1,
							x: [-2, 2, -2, 2, 0],
							y: [2, -2, 2, -2, 0],
						}}
						exit={{
							opacity: 0,
							x: [-2, 2, -2, 2, 0],
							y: [2, -2, 2, -2, 0],
						}}
						transition={{
							opacity: { duration: 0.2 },
							x: { duration: 0.3, times: [0, 0.25, 0.5, 0.75, 1] },
							y: { duration: 0.3, times: [0, 0.25, 0.5, 0.75, 1] },
						}}
						style={{
							left: hoveredTarget.rect.left - 4,
							top: hoveredTarget.rect.top - 4,
							width: hoveredTarget.rect.width + 8,
							height: hoveredTarget.rect.height + 8,
						}}
					/>
				)}
			</AnimatePresence>
		</>
	);
}




