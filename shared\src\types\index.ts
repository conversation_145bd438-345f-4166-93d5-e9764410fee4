export interface Message {
	message: string;
}

export type ApiResponse = {
	message?: string;
	success: true;
};

export interface DiscordActivity {
	id: string;
	name: string;
	type: number;
	state?: string;
	details?: string;
	metadata: Record<string, any>;
	session_id?: string;
	created_at: number;
	application_id?: string;
	timestamps?: {
		start?: number;
		end?: number;
	};
	assets?: {
		large_image?: string;
		large_text?: string;
		small_image?: string;
		small_text?: string;
	};
	platform?: string;
}

export interface DiscordGuild {
	tag: string;
	identity_guild_id: string;
	badge: string;
	identity_enabled: boolean;
}

export interface DiscordUser {
	id: string;
	username: string;
	avatar: string;
	discriminator: string;
	clan: any;
	primary_guild: DiscordGuild;
	avatar_decoration_data: any;
	collectibles: any;
	bot: boolean;
	global_name: string;
	display_name: string;
	public_flags: number;
}

export interface DiscordApiResponse {
	data: {
		kv: Record<string, any>;
		discord_user: DiscordUser;
		activities: DiscordActivity[];
		discord_status: string;
		active_on_discord_web: boolean;
		active_on_discord_desktop: boolean;
		active_on_discord_mobile: boolean;
		active_on_discord_embedded: boolean;
		listening_to_spotify: boolean;
		spotify: any;
	};
	success: boolean;
}
