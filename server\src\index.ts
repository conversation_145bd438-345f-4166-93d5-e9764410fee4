import { db } from "@server/db";
import { messages } from "@server/db/schema";
import { Hono } from "hono";
import { serveStatic } from "hono/bun";
import { cors } from "hono/cors";
import {
	type ApiResponse,
	type DiscordApiResponse,
	generateId,
	type Message,
} from "shared/dist";
import { auth } from "./auth";

export const app = new Hono<{
	Variables: {
		user: typeof auth.$Infer.Session.user | null;
		session: typeof auth.$Infer.Session.session | null;
	};
}>();
export const apiBase = new Hono();

app.use("*", async (c, next) => {
	const session = await auth.api.getSession({ headers: c.req.raw.headers });

	if (!session) {
		c.set("user", null);
		c.set("session", null);
		return next();
	}

	c.set("user", session.user);
	c.set("session", session.session);
	return next();
});

app.use(
	"/api/auth/*",
	cors({
		origin: "http://localhost:5173",
		allowHeaders: ["Content-Type", "Authorization"],
		allowMethods: ["POST", "GET", "OPTIONS"],
		exposeHeaders: ["Content-Length"],
		maxAge: 600,
		credentials: true,
	}),
);

app.on(["POST", "GET"], "/api/auth/**", (c) => auth.handler(c.req.raw));

app.get("/session", (c) => {
	const session = c.get("session");
	const user = c.get("user");

	if (!user) return c.body(null, 401);

	return c.json({
		session,
		user,
	});
});

apiBase
	.post("/send", async (c) => {
		const post = await c.req.json<Message>();
		if (!post) {
			return c.json(
				{ error: "You can't send an empty message, stupid." },
				{ status: 400 },
			);
		}
		const [saveMessage] = await db
			.insert(messages)
			.values({
				id: generateId(32),
				message: post.message,
				createdAt: new Date(),
			})
			.returning();
		if (!saveMessage) {
			return c.json({ error: "Failed to save message." }, { status: 500 });
		}
		const data: ApiResponse = {
			success: true,
			message:
				"I might add a reply feature soon, should I be normal or weird about it... hmmm...",
		};

		return c.json({ data }, { status: 200 });
	})
	.get("/discord", async (c) => {
		try {
			const response = await fetch(
				"http://192.168.0.48:4001/v1/users/1383592267201384469",
			);
			if (!response.ok) {
				throw new Error(`Error fetching Discord Details ${response.status}`);
			}
			const discordData = (await response.json()) as DiscordApiResponse;
			if (discordData.success !== true) {
				throw new Error("Error");
			}

			const data: DiscordApiResponse = {
				...discordData,
				success: true,
			};

			return c.json(data, { status: 200 });
		} catch (error) {
			return c.json({ error: "Failed to fetch Discord data" }, { status: 500 });
		}
	});

app.use(cors());
app.use("*", serveStatic({ root: "./static" }));
app.use("*", async (c, next) => {
	return serveStatic({ root: "./static", path: "index.html" })(c, next);
});

app.route("/api", apiBase);

const port = parseInt(process.env.PORT || "3000");
export type ApiRoutes = typeof apiBase;

export default {
	port,
	fetch: app.fetch,
};

console.log(`🦫 bhvr server running on port ${port}`);
