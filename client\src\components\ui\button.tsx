import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { type MotionProps, motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import * as React from "react";
import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center cursor-target justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-md hover:bg-primary/90 border-secondary/80 border-b-4 border-b-secondary/70 active:border-b-2 active:translate-y-0.5 transition-all duration-150",
        destructive:
          "bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 border-destructive/80 border-b-4 border-b-destructive/70 active:border-b-2 active:translate-y-0.5 transition-all duration-150",
        outline:
          "border bg-background shadow-md hover:bg-accent hover:text-accent-foreground border-border border-b-4 border-b-border/70 active:border-b-2 active:translate-y-0.5 transition-all duration-150",
        secondary:
          "bg-secondary text-secondary-foreground shadow-md hover:bg-secondary/80 border-secondary/80 border-b-4 border-b-secondary/70 active:border-b-2 active:translate-y-0.5 transition-all duration-150",
        ghost:
          "hover:bg-accent hover:text-accent-foreground border-transparent",
        link: "text-primary underline-offset-4 hover:underline border-transparent",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        xl: "h-11 rounded-xl px-8",
        xs: "h-7 rounded-md px-2 text-xs gap-1",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

type MotionButtonPropsType = React.ButtonHTMLAttributes<HTMLButtonElement> &
  VariantProps<typeof buttonVariants> &
  MotionProps;

export interface ButtonProps extends MotionButtonPropsType {
  asChild?: boolean;
  isLoading?: boolean;
  stretch?: boolean;
  enableMotion?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      children,
      stretch = false,
      isLoading = false,
      asChild = false,
      enableMotion = false,
      ...props
    },
    ref,
  ) => {
    const buttonContent = isLoading ? (
      <Loader2 className="size-4 animate-spin" />
    ) : (
      children
    );

    if (asChild) {
      return (
        <Slot
          data-slot="button"
          className={cn(
            buttonVariants({ variant, size, className }),
            stretch && "w-full",
          )}
          ref={ref}
          {...props}
        >
          {buttonContent}
        </Slot>
      );
    }

    if (enableMotion) {
      return (
        <motion.button
          data-slot="button"
          className={cn(
            buttonVariants({ variant, size, className }),
            stretch && "w-full",
          )}
          ref={ref}
          whileTap={
            variant?.includes("ghost") || variant?.includes("link")
              ? undefined
              : { y: 2 }
          }
          {...props}
        >
          {buttonContent}
        </motion.button>
      );
    }

    return (
      <button
        data-slot="button"
        className={cn(
          buttonVariants({ variant, size, className }),
          stretch && "w-full",
        )}
        ref={ref}
        {...props}
      >
        {buttonContent}
      </button>
    );
  },
);

Button.displayName = "Button";

export interface ButtonGroupProps
  extends React.HTMLAttributes<HTMLDivElement> { }

export const ButtonGroup = React.forwardRef<HTMLDivElement, ButtonGroupProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "button-group flex flex-row overflow-hidden rounded-lg border w-fit divide-x",
          "*:rounded-none *:border-none *:border-b-0",
          className,
        )}
        {...props}
      />
    );
  },
);

ButtonGroup.displayName = "ButtonGroup";

export { Button, buttonVariants };
