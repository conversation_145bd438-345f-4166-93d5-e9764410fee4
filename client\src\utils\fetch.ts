import { useQuery } from "@tanstack/react-query";
import type { DiscordApiResponse } from "shared";

export const fetchDiscord = async () => {
	try {
		const response = await fetch("/api/discord", {
			headers: { Accept: "application/json" },
		});
		if (!response.ok) {
			throw new Error("Failed to fetch Discord status");
		}
		const data = (await response.json()) as DiscordApiResponse;
		return { data, success: data?.success === true };
	} catch (error) {
		return {
			data: undefined,
			error,
			success: false,
		};
	}
};

// realtime updates (hook)
export const useDiscordStatus = () => {
	return useQuery({
		queryKey: ["discord-status"],
		queryFn: async () => {
			const response = await fetch("/api/discord", {
				headers: { Accept: "application/json" },
			});
			if (!response.ok) {
				throw new Error("Failed to fetch Discord status");
			}
			return (await response.json()) as DiscordApiResponse;
		},
		refetchInterval: 30000, // 30 seconds
	});
};
