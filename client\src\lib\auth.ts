import { inferAdditionalFields } from "better-auth/client/plugins";
import { createAuthClient } from "better-auth/react";
import { auth } from "server/dist/auth";

export const authClient = createAuthClient({
	baseURL: "http://localhost:3000",
	plugins: [inferAdditionalFields<typeof auth>()],
});

export const { signIn, signUp, signOut, useSession } = authClient;

export async function getUser(headers: Headers) {
	const session = await auth.api.getSession({ headers });
	if (!session) return null;
	return session.user || null;
}
