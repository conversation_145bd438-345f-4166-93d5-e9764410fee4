/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as ProjectsRouteImport } from './routes/projects'
import { Route as DashboardRouteImport } from './routes/dashboard'
import { Route as AboutRouteImport } from './routes/about'
import { Route as authRouteRouteImport } from './routes/(auth)/route'
import { Route as IndexRouteImport } from './routes/index'
import { Route as BlogIndexRouteImport } from './routes/blog/index'
import { Route as BlogIdRouteImport } from './routes/blog/$id'
import { Route as authLoginRouteImport } from './routes/(auth)/login'

const ProjectsRoute = ProjectsRouteImport.update({
  id: '/projects',
  path: '/projects',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRoute = DashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const AboutRoute = AboutRouteImport.update({
  id: '/about',
  path: '/about',
  getParentRoute: () => rootRouteImport,
} as any)
const authRouteRoute = authRouteRouteImport.update({
  id: '/(auth)',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const BlogIndexRoute = BlogIndexRouteImport.update({
  id: '/blog/',
  path: '/blog/',
  getParentRoute: () => rootRouteImport,
} as any)
const BlogIdRoute = BlogIdRouteImport.update({
  id: '/blog/$id',
  path: '/blog/$id',
  getParentRoute: () => rootRouteImport,
} as any)
const authLoginRoute = authLoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => authRouteRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof authRouteRouteWithChildren
  '/about': typeof AboutRoute
  '/dashboard': typeof DashboardRoute
  '/projects': typeof ProjectsRoute
  '/login': typeof authLoginRoute
  '/blog/$id': typeof BlogIdRoute
  '/blog': typeof BlogIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof authRouteRouteWithChildren
  '/about': typeof AboutRoute
  '/dashboard': typeof DashboardRoute
  '/projects': typeof ProjectsRoute
  '/login': typeof authLoginRoute
  '/blog/$id': typeof BlogIdRoute
  '/blog': typeof BlogIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/(auth)': typeof authRouteRouteWithChildren
  '/about': typeof AboutRoute
  '/dashboard': typeof DashboardRoute
  '/projects': typeof ProjectsRoute
  '/(auth)/login': typeof authLoginRoute
  '/blog/$id': typeof BlogIdRoute
  '/blog/': typeof BlogIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/about'
    | '/dashboard'
    | '/projects'
    | '/login'
    | '/blog/$id'
    | '/blog'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/about'
    | '/dashboard'
    | '/projects'
    | '/login'
    | '/blog/$id'
    | '/blog'
  id:
    | '__root__'
    | '/'
    | '/(auth)'
    | '/about'
    | '/dashboard'
    | '/projects'
    | '/(auth)/login'
    | '/blog/$id'
    | '/blog/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  authRouteRoute: typeof authRouteRouteWithChildren
  AboutRoute: typeof AboutRoute
  DashboardRoute: typeof DashboardRoute
  ProjectsRoute: typeof ProjectsRoute
  BlogIdRoute: typeof BlogIdRoute
  BlogIndexRoute: typeof BlogIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/projects': {
      id: '/projects'
      path: '/projects'
      fullPath: '/projects'
      preLoaderRoute: typeof ProjectsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/about': {
      id: '/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof AboutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)': {
      id: '/(auth)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof authRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/blog/': {
      id: '/blog/'
      path: '/blog'
      fullPath: '/blog'
      preLoaderRoute: typeof BlogIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/blog/$id': {
      id: '/blog/$id'
      path: '/blog/$id'
      fullPath: '/blog/$id'
      preLoaderRoute: typeof BlogIdRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)/login': {
      id: '/(auth)/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof authLoginRouteImport
      parentRoute: typeof authRouteRoute
    }
  }
}

interface authRouteRouteChildren {
  authLoginRoute: typeof authLoginRoute
}

const authRouteRouteChildren: authRouteRouteChildren = {
  authLoginRoute: authLoginRoute,
}

const authRouteRouteWithChildren = authRouteRoute._addFileChildren(
  authRouteRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  authRouteRoute: authRouteRouteWithChildren,
  AboutRoute: AboutRoute,
  DashboardRoute: DashboardRoute,
  ProjectsRoute: ProjectsRoute,
  BlogIdRoute: BlogIdRoute,
  BlogIndexRoute: BlogIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
