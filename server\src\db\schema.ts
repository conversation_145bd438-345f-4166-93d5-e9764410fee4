import { pgTable, text, timestamp } from "drizzle-orm/pg-core";

export const messages = pgTable("messages", {
	id: text("id").primaryKey(),
	message: text("message").notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

// export const posts = pgTable(
//   "blogs",
//   {
//     id: text("id").primaryKey(),
//     authorId: text("author_id") // Using serial for simplicity, typically foreign key to users.id
//       .notNull()
//       .references(() => users.id, { onDelete: "cascade" }), // Cascade delete if user is removed
//     title: text("title").notNull(),
//     slug: text("slug").notNull().unique(), // Unique slug for clean URLs
//     content: text("content").notNull(), // Markdown or HTML content
//     excerpt: text("excerpt"), // Short summary of the post
//     // status: postStatusEnum("status").notNull().default("draft"), // Using the enum
//     published: boolean("published").notNull().default(false), // Simple published status
//     tags: text("tags").array(), // Store tags as an array of strings (PostgreSQL specific)
//     imageUrl: text("image_url"), // Optional URL for a featured image
//     createdAt: timestamp("created_at").notNull().defaultNow(),
//     updatedAt: timestamp("updated_at").notNull().defaultNow(),
//     publishedAt: timestamp("published_at"), // When the post was actually published
//   },
//   (table) => {
//     return {
//       slugIdx: uniqueIndex("slug_idx").on(table.slug), // Ensure slug is unique
//       authorIdIdx: uniqueIndex("author_id_idx").on(table.authorId),
//     };
//   },
// );
