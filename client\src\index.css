@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(0.9821 0 0);
  --foreground: oklch(0.2435 0 0);
  --card: oklch(0.9911 0 0);
  --card-foreground: oklch(0.2435 0 0);
  --popover: oklch(0.9911 0 0);
  --popover-foreground: oklch(0.2435 0 0);
  --primary: oklch(0.3975 0.0607 296.2993);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.8352 0.1102 309.9643);
  --secondary-foreground: oklch(0.2879 0.1014 291.1752);
  --muted: oklch(0.9521 0 0);
  --muted-foreground: oklch(0.5032 0 0);
  --accent: oklch(0.9310 0 0);
  --accent-foreground: oklch(0.2435 0 0);
  --destructive: oklch(0.4793 0.2523 278.7335);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8822 0 0);
  --input: oklch(0.8822 0 0);
  --ring: oklch(0.3975 0.0607 296.2993);
  --chart-1: oklch(0.3975 0.0607 296.2993);
  --chart-2: oklch(0.8352 0.1102 309.9643);
  --chart-3: oklch(0.9310 0 0);
  --chart-4: oklch(0.8684 0.0877 310.6120);
  --chart-5: oklch(0.3933 0.0674 295.7950);
  --sidebar: oklch(0.9881 0 0);
  --sidebar-foreground: oklch(0.2645 0 0);
  --sidebar-primary: oklch(0.3250 0 0);
  --sidebar-primary-foreground: oklch(0.9881 0 0);
  --sidebar-accent: oklch(0.9761 0 0);
  --sidebar-accent-foreground: oklch(0.3250 0 0);
  --sidebar-border: oklch(0.9401 0 0);
  --sidebar-ring: oklch(0.7731 0 0);
  --font-sans: Outfit, sans-serif;
  --font-serif: Architects Daughter, sans-serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.1776 0 0);
  --foreground: oklch(0.9491 0 0);
  --card: oklch(0.2134 0 0);
  --card-foreground: oklch(0.9491 0 0);
  --popover: oklch(0.2134 0 0);
  --popover-foreground: oklch(0.9491 0 0);
  --primary: oklch(0.8595 0.0888 306.9497);
  --primary-foreground: oklch(0.2645 0.0434 110.9625);
  --secondary: oklch(0.2931 0.0322 305.9326);
  --secondary-foreground: oklch(0.8595 0.0888 306.9497);
  --muted: oklch(0.2520 0 0);
  --muted-foreground: oklch(0.7699 0 0);
  --accent: oklch(0.2850 0 0);
  --accent-foreground: oklch(0.9491 0 0);
  --destructive: oklch(0.4793 0.2523 278.7335);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.2197 0.0178 317.6292);
  --input: oklch(0.4017 0 0);
  --ring: oklch(0.8595 0.0888 306.9497);
  --chart-1: oklch(0.8595 0.0888 306.9497);
  --chart-2: oklch(0.2931 0.0322 305.9326);
  --chart-3: oklch(0.2850 0 0);
  --chart-4: oklch(0.3207 0.0373 307.1609);
  --chart-5: oklch(0.8577 0.0905 307.2844);
  --sidebar: oklch(0.2177 0.0073 145.3172);
  --sidebar-foreground: oklch(0.9691 0.0017 145.5620);
  --sidebar-primary: oklch(0.7762 0.2400 139.7730);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.2809 0.0069 145.3885);
  --sidebar-accent-foreground: oklch(0.9691 0.0017 145.5620);
  --sidebar-border: oklch(0.2809 0.0069 145.3885);
  --sidebar-ring: oklch(0.8780 0.0069 145.5162);
  --font-sans: Outfit, sans-serif;
  --font-serif: Architects Daughter, sans-serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

body {
  letter-spacing: var(--tracking-normal);
  cursor: none;
}

* {
  cursor: none !important;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@keyframes grid-draw {
  0% {
    stroke-dashoffset: 1000;
    opacity: 0;
  }

  50% {
    opacity: 0.3;
  }

  100% {
    stroke-dashoffset: 0;
    opacity: 0.15;
  }
}

@keyframes pop-blob {
  0% {
    transform: scale(1);
  }
  33% {
    transform: scale(1.2);
  }
  66% {
    transform: scale(0.8);
  }
  100% {
    transform: scale(1);
  }
}
